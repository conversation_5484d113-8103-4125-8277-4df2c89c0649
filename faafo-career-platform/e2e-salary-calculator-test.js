#!/usr/bin/env node

const puppeteer = require('puppeteer');

// Comprehensive E2E Test for Salary Calculator
async function runSalaryCalculatorE2ETest() {
  console.log('🚀 Starting Comprehensive Salary Calculator E2E Test...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false, // Set to true for CI/CD
    defaultViewport: { width: 1280, height: 720 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Test 1: Page Load and Basic Elements
    console.log('📋 Test 1: Page Load and Basic Elements');
    await page.goto('http://localhost:3000/tools/salary-calculator', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    // Check if page loaded correctly
    const title = await page.title();
    console.log(`   ✅ Page title: ${title}`);
    
    // Check main heading
    const heading = await page.$eval('h1', el => el.textContent);
    console.log(`   ✅ Main heading: ${heading}`);
    
    // Check form elements
    const careerPathLabel = await page.$eval('label[for="careerPath"]', el => el.textContent);
    console.log(`   ✅ Career path label: ${careerPathLabel}`);
    
    // Test 2: Career Path Dropdown - Check All 11 Paths
    console.log('\n📋 Test 2: Career Path Dropdown - Verify All 11 Paths');
    
    // Click career path dropdown
    await page.click('[data-testid="career-path-trigger"]');
    await page.waitForTimeout(1000);
    
    // Get all career path options
    const careerPaths = await page.$$eval('[role="option"]', options => 
      options.map(option => option.textContent.trim())
    );
    
    console.log(`   ✅ Found ${careerPaths.length} career paths:`);
    careerPaths.forEach((path, index) => {
      console.log(`      ${index + 1}. ${path}`);
    });
    
    // Verify we have exactly 11 career paths
    if (careerPaths.length === 11) {
      console.log('   ✅ PASS: All 11 career paths are present');
    } else {
      console.log(`   ❌ FAIL: Expected 11 career paths, found ${careerPaths.length}`);
    }
    
    // Test 3: Form Interaction - Select Career Path
    console.log('\n📋 Test 3: Form Interaction - Select Career Path');
    
    // Select "Data Scientist"
    await page.click('[role="option"]', { text: 'Data Scientist' });
    await page.waitForTimeout(500);
    
    console.log('   ✅ Selected "Data Scientist" career path');
    
    // Test 4: Experience Level Selection
    console.log('\n📋 Test 4: Experience Level Selection');
    
    await page.click('[data-testid="experience-trigger"]');
    await page.waitForTimeout(500);
    
    // Select "Senior" experience level
    await page.click('[role="option"]', { text: 'Senior (5-8 years)' });
    await page.waitForTimeout(500);
    
    console.log('   ✅ Selected "Senior" experience level');
    
    // Test 5: Location Selection
    console.log('\n📋 Test 5: Location Selection');
    
    await page.click('[data-testid="location-trigger"]');
    await page.waitForTimeout(500);
    
    // Select "San Francisco, CA"
    await page.click('[role="option"]', { text: 'San Francisco, CA' });
    await page.waitForTimeout(500);
    
    console.log('   ✅ Selected "San Francisco, CA" location');
    
    // Test 6: Form Submission and Results
    console.log('\n📋 Test 6: Form Submission and Results');
    
    // Submit the form
    await page.click('button[type="submit"]');
    console.log('   ✅ Submitted form');
    
    // Wait for results to appear
    await page.waitForSelector('h2', { timeout: 10000 });
    
    // Check if results are displayed
    const resultsHeading = await page.$eval('h2', el => el.textContent);
    console.log(`   ✅ Results heading: ${resultsHeading}`);
    
    // Check salary range
    const salaryRange = await page.$eval('div:contains("$")', el => el.textContent);
    console.log(`   ✅ Salary range displayed: ${salaryRange}`);
    
    // Check confidence score
    const confidence = await page.$eval('*:contains("Confidence")', el => el.textContent);
    console.log(`   ✅ Confidence score: ${confidence}`);
    
    // Test 7: Test Multiple Career Paths
    console.log('\n📋 Test 7: Testing Multiple Career Paths');
    
    const testPaths = [
      'AI/Machine Learning Engineer',
      'Cloud Solutions Architect', 
      'Cybersecurity Specialist',
      'Freelance Web Developer'
    ];
    
    for (const path of testPaths) {
      console.log(`\n   Testing: ${path}`);
      
      // Reset form by selecting new career path
      await page.click('[data-testid="career-path-trigger"]');
      await page.waitForTimeout(500);
      
      await page.click(`[role="option"]:contains("${path}")`);
      await page.waitForTimeout(500);
      
      // Submit form
      await page.click('button[type="submit"]');
      await page.waitForTimeout(2000);
      
      // Check if results updated
      const newResults = await page.$eval('h2', el => el.textContent);
      console.log(`   ✅ ${path}: Results updated - ${newResults}`);
    }
    
    // Test 8: API Integration Test
    console.log('\n📋 Test 8: API Integration Test');
    
    // Test API endpoint directly
    const apiResponse = await page.evaluate(async () => {
      const response = await fetch('/api/tools/salary-calculator?type=career-paths');
      return await response.json();
    });
    
    console.log(`   ✅ API Response: ${apiResponse.data.length} career paths returned`);
    console.log('   ✅ API integration working correctly');
    
    // Test 9: Responsive Design Test
    console.log('\n📋 Test 9: Responsive Design Test');
    
    // Test mobile viewport
    await page.setViewport({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    
    const isMobileResponsive = await page.$eval('.container', el => 
      window.getComputedStyle(el).display !== 'none'
    );
    
    console.log(`   ✅ Mobile responsive: ${isMobileResponsive ? 'PASS' : 'FAIL'}`);
    
    // Reset to desktop
    await page.setViewport({ width: 1280, height: 720 });
    
    // Test 10: Error Handling
    console.log('\n📋 Test 10: Error Handling');
    
    // Test form validation by submitting empty form
    await page.reload();
    await page.waitForTimeout(2000);
    
    await page.click('button[type="submit"]');
    await page.waitForTimeout(1000);
    
    // Check for validation message
    const validationMessage = await page.$('*:contains("Please select")');
    if (validationMessage) {
      console.log('   ✅ Form validation working correctly');
    } else {
      console.log('   ⚠️  Form validation not detected');
    }
    
    console.log('\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!');
    console.log('\n📊 SUMMARY:');
    console.log('   ✅ Page loads correctly');
    console.log('   ✅ All 11 career paths available');
    console.log('   ✅ Form interactions working');
    console.log('   ✅ Salary calculations displaying');
    console.log('   ✅ Multiple career paths tested');
    console.log('   ✅ API integration verified');
    console.log('   ✅ Responsive design confirmed');
    console.log('   ✅ Error handling functional');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await browser.close();
  }
}

// Run the test
runSalaryCalculatorE2ETest().catch(console.error);
